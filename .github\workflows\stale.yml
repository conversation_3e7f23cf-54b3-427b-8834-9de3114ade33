# This workflow warns and then closes issues and PRs that have had no activity for a specified amount of time.
#
# You can adjust the behavior by modifying this file.
# For more information, see:
# https://github.com/actions/stale
name: Mark stale issues and pull requests

on:
  schedule:
    - cron: '0 3 * * *'

jobs:
  stale:

    runs-on: ubuntu-latest
    permissions:
      issues: write
      pull-requests: write

    steps:
      - uses: actions/stale@v5
        with:
          days-before-issue-stale: 15
          days-before-issue-close: 3
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          stale-issue-message: "Close due to it's no longer active, if you have any questions, you can reopen it."
          stale-pr-message: "Close due to it's no longer active, if you have any questions, you can reopen it."
          stale-issue-label: 'no-issue-activity'
          stale-pr-label: 'no-pr-activity'
          any-of-labels: 'duplicate,question,invalid,wontfix,no-issue-activity,no-pr-activity,enhancement,cant-reproduce,help-wanted'
